{"version": 3, "file": "use-sound.cjs.production.min.js", "sources": ["../src/index.ts", "../src/use-on-mount.ts"], "sourcesContent": ["import React from 'react';\n\nimport useOnMount from './use-on-mount';\n\nimport { HookOptions, PlayOptions, PlayFunction, ReturnedValue } from './types';\n\nexport default function useSound<T = any>(\n  src: string | string[],\n  {\n    id,\n    volume = 1,\n    playbackRate = 1,\n    soundEnabled = true,\n    interrupt = false,\n    onload,\n    ...delegated\n  }: HookOptions<T> = {} as HookOptions\n) {\n  const HowlConstructor = React.useRef<HowlStatic | null>(null);\n  const isMounted = React.useRef(false);\n\n  const [duration, setDuration] = React.useState<number | null>(null);\n\n  const [sound, setSound] = React.useState<Howl | null>(null);\n\n  const handleLoad = function() {\n    if (typeof onload === 'function') {\n      // @ts-ignore\n      onload.call(this);\n    }\n\n    if (isMounted.current) {\n      // @ts-ignore\n      setDuration(this.duration() * 1000);\n    }\n\n    // @ts-ignore\n    setSound(this);\n  };\n\n  // We want to lazy-load How<PERSON>, since sounds can't play on load anyway.\n  useOnMount(() => {\n    import('howler').then(mod => {\n      if (!isMounted.current) {\n        // Depending on the module system used, `mod` might hold\n        // the export directly, or it might be under `default`.\n        HowlConstructor.current = mod.Howl ?? mod.default.Howl;\n\n        isMounted.current = true;\n\n        new HowlConstructor.current({\n          src: Array.isArray(src) ? src : [src],\n          volume,\n          rate: playbackRate,\n          onload: handleLoad,\n          ...delegated,\n        });\n      }\n    });\n\n    return () => {\n      isMounted.current = false;\n    };\n  });\n\n  // When the `src` changes, we have to do a whole thing where we recreate\n  // the Howl instance. This is because Howler doesn't expose a way to\n  // tweak the sound\n  React.useEffect(() => {\n    if (HowlConstructor.current && sound) {\n      setSound(\n        new HowlConstructor.current({\n          src: Array.isArray(src) ? src : [src],\n          volume,\n          onload: handleLoad,\n          ...delegated,\n        })\n      );\n    }\n    // The linter wants to run this effect whenever ANYTHING changes,\n    // but very specifically I only want to recreate the Howl instance\n    // when the `src` changes. Other changes should have no effect.\n    // Passing array to the useEffect dependencies list will result in\n    // ifinite loop so we need to stringify it, for more details check\n    // https://github.com/facebook/react/issues/14476#issuecomment-471199055\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [JSON.stringify(src)]);\n\n  // Whenever volume/playbackRate are changed, change those properties\n  // on the sound instance.\n  React.useEffect(() => {\n    if (sound) {\n      sound.volume(volume);\n\n      // HACK: When a sprite is defined, `sound.rate()` throws an error, because Howler tries to reset the \"_default\" sprite, which doesn't exist. This is likely a bug within Howler, but I don’t have the bandwidth to investigate, so instead, we’re ignoring playbackRate changes when a sprite is defined.\n      if (!delegated.sprite) {\n        sound.rate(playbackRate);\n      }\n    }\n  }, [sound, volume, playbackRate]);\n\n  const play: PlayFunction = React.useCallback(\n    (options?: PlayOptions) => {\n      if (typeof options === 'undefined') {\n        options = {};\n      }\n\n      if (!sound || (!soundEnabled && !options.forceSoundEnabled)) {\n        return;\n      }\n\n      if (interrupt) {\n        sound.stop();\n      }\n\n      if (options.playbackRate) {\n        sound.rate(options.playbackRate);\n      }\n\n      sound.play(options.id);\n    },\n    [sound, soundEnabled, interrupt]\n  );\n\n  const stop = React.useCallback(\n    id => {\n      if (!sound) {\n        return;\n      }\n      sound.stop(id);\n    },\n    [sound]\n  );\n\n  const pause = React.useCallback(\n    id => {\n      if (!sound) {\n        return;\n      }\n      sound.pause(id);\n    },\n    [sound]\n  );\n\n  const returnedValue: ReturnedValue = [\n    play,\n    {\n      sound,\n      stop,\n      pause,\n      duration,\n    },\n  ];\n\n  return returnedValue;\n}\n\nexport { useSound };\n", "import * as React from 'react';\n\nexport default function useOnMount(callback: React.EffectCallback) {\n  React.useEffect(callback, []);\n}\n"], "names": ["useSound", "src", "_temp", "id", "_ref", "volume", "_ref$volume", "_ref$playbackRate", "playbackRate", "_ref$soundEnabled", "soundEnabled", "_ref$interrupt", "interrupt", "onload", "delegated", "_objectWithoutPropertiesLoose", "_excluded", "HowlConstructor", "React", "useRef", "isMounted", "_React$useState", "useState", "duration", "setDuration", "_React$useState2", "sound", "setSound", "handleLoad", "call", "this", "current", "then", "mod", "_mod$Howl", "Howl", "_extends", "Array", "isArray", "rate", "useEffect", "JSON", "stringify", "sprite", "play", "useCallback", "options", "forceSoundEnabled", "stop", "pause"], "mappings": "6bAMwBA,EACtBC,EAAsBC,oBASF,GAAiBA,EAPnCC,EAAEC,EACFC,OAAAA,WAAMC,EAAG,EAACA,EAAAC,EAAAH,EACVI,aAAAA,WAAYD,EAAG,EAACA,EAAAE,EAAAL,EAChBM,aAAAA,WAAYD,GAAOA,EAAAE,EAAAP,EACnBQ,UAAAA,WAASD,GAAQA,EACjBE,EAAMT,EAANS,OACGC,6IAASC,CAAAX,EAAAY,GAGRC,EAAkBC,EAAMC,OAA0B,MAClDC,EAAYF,EAAMC,QAAO,GAE/BE,EAAgCH,EAAMI,SAAwB,MAAvDC,EAAQF,KAAEG,EAAWH,KAE5BI,EAA0BP,EAAMI,SAAsB,MAA/CI,EAAKD,KAAEE,EAAQF,KAEhBG,EAAa,WACK,mBAAXf,GAETA,EAAOgB,KAAKC,MAGVV,EAAUW,SAEZP,EAA8B,IAAlBM,KAAKP,YAInBI,EAASG,OClCXZ,aDsCW,WAmBT,OAlBA,8QAAO,eAAUc,MAAK,SAAAC,GACI,IAAAC,EAAnBd,EAAUW,UAGbd,EAAgBc,gBAAOG,EAAGD,EAAIE,gBAAID,EAAAA,EAAID,UAAYE,KAElDf,EAAUW,SAAU,EAEpB,IAAId,EAAgBc,QAAOK,GACzBnC,IAAKoC,MAAMC,QAAQrC,GAAOA,EAAM,CAACA,GACjCI,OAAAA,EACAkC,KAAM/B,EACNK,OAAQe,GACLd,QAKF,WACLM,EAAUW,SAAU,KC1DE,IDiE1Bb,EAAMsB,WAAU,WACVvB,EAAgBc,SAAWL,GAC7BC,EACE,IAAIV,EAAgBc,QAAOK,GACzBnC,IAAKoC,MAAMC,QAAQrC,GAAOA,EAAM,CAACA,GACjCI,OAAAA,EACAQ,OAAQe,GACLd,OAWR,CAAC2B,KAAKC,UAAUzC,KAInBiB,EAAMsB,WAAU,WACVd,IACFA,EAAMrB,OAAOA,GAGRS,EAAU6B,QACbjB,EAAMa,KAAK/B,MAGd,CAACkB,EAAOrB,EAAQG,IAEnB,IAAMoC,EAAqB1B,EAAM2B,aAC/B,SAACC,QACwB,IAAZA,IACTA,EAAU,IAGPpB,IAAWhB,GAAiBoC,EAAQC,qBAIrCnC,GACFc,EAAMsB,OAGJF,EAAQtC,cACVkB,EAAMa,KAAKO,EAAQtC,cAGrBkB,EAAMkB,KAAKE,EAAQ3C,OAErB,CAACuB,EAAOhB,EAAcE,IAGlBoC,EAAO9B,EAAM2B,aACjB,SAAA1C,GACOuB,GAGLA,EAAMsB,KAAK7C,KAEb,CAACuB,IAGGuB,EAAQ/B,EAAM2B,aAClB,SAAA1C,GACOuB,GAGLA,EAAMuB,MAAM9C,KAEd,CAACuB,IAaH,MAVqC,CACnCkB,EACA,CACElB,MAAAA,EACAsB,KAAAA,EACAC,MAAAA,EACA1B,SAAAA"}