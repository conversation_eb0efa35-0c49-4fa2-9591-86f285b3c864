"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e,n=require("react"),r=(e=n)&&"object"==typeof e&&"default"in e?e.default:e;function t(){return(t=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)({}).hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e}).apply(null,arguments)}var u=["id","volume","playbackRate","soundEnabled","interrupt","onload"];function o(e,o){var a=void 0===o?{}:o,i=a.volume,l=void 0===i?1:i,c=a.playbackRate,f=void 0===c?1:c,s=a.soundEnabled,d=void 0===s||s,p=a.interrupt,v=void 0!==p&&p,b=a.onload,y=function(e,n){if(null==e)return{};var r={};for(var t in e)if({}.hasOwnProperty.call(e,t)){if(-1!==n.indexOf(t))continue;r[t]=e[t]}return r}(a,u),O=r.useRef(null),h=r.useRef(!1),w=r.useState(null),k=w[0],g=w[1],j=r.useState(null),m=j[0],E=j[1],P=function(){"function"==typeof b&&b.call(this),h.current&&g(1e3*this.duration()),E(this)};n.useEffect((function(){return new Promise((function(e){e(function(e){if(e&&e.__esModule)return e;var n={};return e&&Object.keys(e).forEach((function(r){var t=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,t.get?t:{enumerable:!0,get:function(){return e[r]}})})),n.default=e,n}(require("howler")))})).then((function(n){var r;h.current||(O.current=null!==(r=n.Howl)&&void 0!==r?r:n.default.Howl,h.current=!0,new O.current(t({src:Array.isArray(e)?e:[e],volume:l,rate:f,onload:P},y)))})),function(){h.current=!1}}),[]),r.useEffect((function(){O.current&&m&&E(new O.current(t({src:Array.isArray(e)?e:[e],volume:l,onload:P},y)))}),[JSON.stringify(e)]),r.useEffect((function(){m&&(m.volume(l),y.sprite||m.rate(f))}),[m,l,f]);var R=r.useCallback((function(e){void 0===e&&(e={}),m&&(d||e.forceSoundEnabled)&&(v&&m.stop(),e.playbackRate&&m.rate(e.playbackRate),m.play(e.id))}),[m,d,v]),S=r.useCallback((function(e){m&&m.stop(e)}),[m]),x=r.useCallback((function(e){m&&m.pause(e)}),[m]);return[R,{sound:m,stop:S,pause:x,duration:k}]}exports.default=o,exports.useSound=o;
//# sourceMappingURL=use-sound.cjs.production.min.js.map
