import React, { useState, useEffect, useRef } from 'react';

function App() {
  const [logs, setLogs] = useState([]);
  const [input, setInput] = useState('');
  const [processing, setProcessing] = useState(false);
  const [confirmed, setConfirmed] = useState(false);
  const [glitchIntensity, setGlitchIntensity] = useState(0);
  const audioContextRef = useRef(null);

  useEffect(() => {
    // Initialize audio context for beep sounds
    audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    glitchIntro();
  }, []);

  const glitchIntro = () => {
    const intro = [
      '[BOOT] Initializing Surveillance Framework...',
      '[SYS] Connecting to Social Graph...',
      '[LOG] Backdoor loaded. Injecting trace listeners...',
      '[STATUS] Access granted to linked metadata...',
      '[ALERT] Anomalous digital footprint detected...',
      '[SCAN] Cross-referencing shadow profiles...',
      '[PROMPT] Possible unknown social activity detected. Wanna investigate?',
    ];

    intro.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);
        playBeep();
        // Increase glitch intensity during boot
        setGlitchIntensity(Math.random() * 3);
        setTimeout(() => setGlitchIntensity(0), 200);
      }, index * 1200);
    });
  };

  const playBeep = () => {
    if (!audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);

    oscillator.frequency.setValueAtTime(800, audioContextRef.current.currentTime);
    gainNode.gain.setValueAtTime(0.1, audioContextRef.current.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContextRef.current.currentTime + 0.1);

    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + 0.1);
  };

  const handleInput = (e) => {
    if (e.key === 'Enter') {
      const command = input.trim().toLowerCase().replace(/"/g, '');
      setLogs((prev) => [...prev, `> ${command}`]);
      setInput('');
      playBeep();

      if (!confirmed && (command === 'yes' || command === 'y')) {
        setConfirmed(true);
        simulateFinding();
      } else if (!confirmed) {
        setLogs((prev) => [...prev, '[INFO] Unknown command. Type "yes" to continue investigation.']);
      } else {
        setLogs((prev) => [...prev, '[INFO] Investigation complete. Refresh to restart.']);
      }
    }
  };

  const simulateFinding = () => {
    setProcessing(true);
    const steps = [
      '[TASK] Initializing deep scan protocols...',
      '[SCAN] Searching shadow profiles across networks...',
      '[TRACE] Correlating contact sync data...',
      '[DECRYPT] Breaking through privacy barriers...',
      '[MATCH] Unregistered link found in shared messages...',
      '[INFO] Extracting hidden metadata patterns...',
      '[ANALYZE] Cross-referencing behavioral signatures...',
      '[DATA] Account age: 3 weeks | Activity: Sporadic | Visibility: Hidden',
      '[PATTERN] Posting schedule matches target user...',
      '[LOCATION] Geotag correlation: 94% match',
      '[✔] HIGH CONFIDENCE: Probable alt-account detected',
      '[FOUND] Username: @shyy_girl_meta_23',
      '[PROFILE] Bio: "just vibes ✨" | Followers: 47 | Following: 12',
      '[ACTIVITY] Last seen: 2 hours ago',
      '[LOG] Suspicious activity tagged. Recommendation: Monitor discreetly.',
      '[COMPLETE] Investigation finished. Data archived.',
    ];

    steps.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);
        playBeep();

        // Add dramatic glitch effects during critical moments
        if (line.includes('FOUND') || line.includes('DECRYPT') || line.includes('HIGH CONFIDENCE')) {
          setGlitchIntensity(5);
          setTimeout(() => setGlitchIntensity(1), 500);
        }

        if (index === steps.length - 1) {
          setProcessing(false);
          setGlitchIntensity(0);
        }
      }, index * 1500);
    });
  };

  return (
    <div
      style={{
        backgroundColor: '#000',
        color: '#00ff88',
        fontFamily: '"Courier New", monospace',
        minHeight: '100vh',
        padding: '20px',
        overflow: 'hidden',
        position: 'relative',
        animation: glitchIntensity > 0 ? `glitchAnim ${0.1 + glitchIntensity * 0.05}s infinite step-start` : 'none',
      }}
    >
      <style>{`
        @keyframes glitchAnim {
          0% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(0);
          }
          10% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          20% {
            text-shadow: 2px -2px #ff0000, -2px 2px #00ffff;
            transform: translate(2px, -1px);
          }
          30% {
            text-shadow: -2px 2px #ff0000, 2px -2px #00ffff;
            transform: translate(-1px, 2px);
          }
          40% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(1px, -2px);
          }
          50% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          60% {
            text-shadow: 2px -2px #ff0000, -2px 2px #00ffff;
            transform: translate(2px, -1px);
          }
          70% {
            text-shadow: -2px 2px #ff0000, 2px -2px #00ffff;
            transform: translate(-1px, 2px);
          }
          80% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(1px, -1px);
          }
          90% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          100% {
            text-shadow: none;
            transform: translate(0);
          }
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        .terminal-cursor {
          animation: blink 1s infinite;
        }

        .critical-line {
          color: #ff4444;
          font-weight: bold;
        }

        .success-line {
          color: #44ff44;
          font-weight: bold;
        }

        .warning-line {
          color: #ffaa00;
          font-weight: bold;
        }
      `}</style>

      <div style={{
        borderBottom: '1px solid #00ff88',
        paddingBottom: '10px',
        marginBottom: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h2 style={{ margin: 0 }}>⚠️ MetaTrace Terminal - vGhostMode.91</h2>
        <div style={{ fontSize: '12px', opacity: 0.7 }}>
          {processing ? '🔍 SCANNING...' : '⚡ READY'}
        </div>
      </div>

      <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.4' }}>
        {logs.map((line, index) => {
          let className = '';
          if (line.includes('[FOUND]') || line.includes('HIGH CONFIDENCE') || line.includes('✔')) {
            className = 'success-line';
          } else if (line.includes('[ALERT]') || line.includes('[DECRYPT]') || line.includes('SUSPICIOUS')) {
            className = 'critical-line';
          } else if (line.includes('[PROMPT]') || line.includes('[ANALYZE]')) {
            className = 'warning-line';
          }

          return (
            <div key={index} className={className} style={{ marginBottom: '2px' }}>
              {line}
            </div>
          );
        })}

        {processing && (
          <div style={{ marginTop: '10px', opacity: 0.8 }}>
            <span className="terminal-cursor">█</span> Processing...
          </div>
        )}

        {!processing && (
          <div style={{ display: 'flex', alignItems: 'center', marginTop: '15px' }}>
            <span style={{ color: '#ffaa00' }}>{'> '}</span>
            <input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleInput}
              style={{
                backgroundColor: 'transparent',
                color: '#00ff88',
                border: 'none',
                outline: 'none',
                fontFamily: '"Courier New", monospace',
                fontSize: '16px',
                flex: 1,
                caretColor: '#00ff88',
              }}
              placeholder={confirmed ? "Investigation complete..." : "Type 'yes' to investigate..."}
              disabled={confirmed}
              autoFocus
            />
            <span className="terminal-cursor" style={{ marginLeft: '5px' }}>█</span>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
