import React, { useState, useEffect, useRef } from 'react';

function App() {
  const [logs, setLogs] = useState([]);
  const [input, setInput] = useState('');
  const [processing, setProcessing] = useState(false);
  const [confirmed, setConfirmed] = useState(false);
  const [glitchIntensity, setGlitchIntensity] = useState(0);
  const audioContextRef = useRef(null);

  useEffect(() => {
    // Initialize audio context for beep sounds
    try {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported');
    }
    glitchIntro();
  }, []);

  const glitchIntro = () => {
    const intro = [
      '[BOOT] Initializing Surveillance Framework v3.7.2...',
      '[KERNEL] Loading neural network modules... OK',
      '[MEMORY] Allocating 2048MB for deep scan operations... OK',
      '[NETWORK] Establishing encrypted tunnels to data centers...',
      '[PROXY] Routing through 7 international proxy servers...',
      '[STEALTH] Activating ghost mode protocols...',
      '[DATABASE] Connecting to global social media archives...',
      '[AI] Loading behavioral pattern recognition engine...',
      '[CRYPTO] Decrypting protected metadata streams...',
      '[SYS] Social Graph API initialized successfully',
      '[LOG] Backdoor loaded. Injecting trace listeners...',
      '[MONITOR] Real-time activity surveillance: ACTIVE',
      '[SCANNER] Cross-platform profile correlation: READY',
      '[STATUS] Access granted to linked metadata repositories',
      '[ALERT] Anomalous digital footprint patterns detected',
      '[WARNING] Potential shadow account activity identified',
      '[SCAN] Cross-referencing 847,293 shadow profiles...',
      '[ANALYSIS] Behavioral fingerprint matching in progress...',
      '[INTEL] Suspicious Instagram activity patterns detected',
      '[TARGET] Potential hidden account identified in network',
      '[READY] All systems operational. Surveillance mode: ACTIVE',
      '',
      '[🔍 DETECTION ALERT 🔍]',
      '[PROMPT] Possible unknown Instagram account detected. Wanna investigate?',
    ];

    intro.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);
        playBeep();
        // Increase glitch intensity during critical moments
        if (line.includes('ALERT') || line.includes('WARNING') || line.includes('READY')) {
          setGlitchIntensity(Math.random() * 4 + 1);
          setTimeout(() => setGlitchIntensity(0), 300);
        }
      }, index * 800);
    });
  };

  const playBeep = (type = 'normal') => {
    if (!audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);

    // Different beep types for different events
    let frequency = 800;
    let duration = 0.1;
    let volume = 0.1;

    switch (type) {
      case 'critical':
        frequency = 1200;
        duration = 0.2;
        volume = 0.15;
        break;
      case 'success':
        frequency = 600;
        duration = 0.15;
        volume = 0.12;
        break;
      case 'warning':
        frequency = 1000;
        duration = 0.12;
        volume = 0.13;
        break;
      default:
        frequency = 800;
        duration = 0.08;
        volume = 0.08;
    }

    oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
    gainNode.gain.setValueAtTime(volume, audioContextRef.current.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContextRef.current.currentTime + duration);

    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + duration);
  };

  const handleInput = (e) => {
    if (e.key === 'Enter') {
      const command = input.trim().toLowerCase().replace(/"/g, '');
      setLogs((prev) => [...prev, `> ${command}`]);
      setInput('');
      playBeep();

      if (!confirmed && (command === '&find' || command === 'find' || command === '&scan')) {
        setConfirmed(true);
        simulateFinding();
      } else if (!confirmed) {
        setLogs((prev) => [...prev, '[ERROR] Invalid command. Access denied.']);
      } else {
        setLogs((prev) => [...prev, '[SYSTEM] Session terminated. Restart required.']);
      }
    }
  };

  const simulateFinding = () => {
    setProcessing(true);
    const steps = [
      '[INIT] Launching deep reconnaissance protocols...',
      '[STEALTH] Activating invisible scanning mode...',
      '[NETWORK] Penetrating social media firewalls...',
      '[BYPASS] Circumventing privacy protection layers...',
      '[ACCESS] Gained entry to restricted databases...',
      '[CRAWL] Scanning 2,847,392 user profiles...',
      '[FILTER] Applying behavioral pattern algorithms...',
      '[TRACE] Following digital breadcrumbs...',
      '[DECRYPT] Breaking 256-bit encryption barriers...',
      '[METADATA] Extracting hidden EXIF data from images...',
      '[GEOLOC] Triangulating location data from posts...',
      '[TIMELINE] Analyzing posting frequency patterns...',
      '[CONTACTS] Cross-referencing mutual connections...',
      '[DEVICE] Fingerprinting device signatures...',
      '[IP] Tracing network routing patterns...',
      '[BEHAVIOR] Matching linguistic patterns...',
      '[CORRELATION] Found 94.7% behavioral similarity...',
      '[MATCH] Unregistered shadow account detected!',
      '[BREACH] Accessing private message history...',
      '[EXTRACT] Downloading profile metadata...',
      '[ANALYZE] Processing 1,247 data points...',
      '[PATTERN] Identical posting schedule detected',
      '[LOCATION] GPS coordinates match 94% of the time',
      '[CONTACTS] 23 mutual friends identified',
      '[PHOTOS] Facial recognition: 97.3% match confidence',
      '[STYLE] Writing pattern analysis: IDENTICAL',
      '[TIMING] Active hours correlation: PERFECT MATCH',
      '',
      '[⚠️ CRITICAL ALERT ⚠️] HIGH CONFIDENCE DETECTION',
      '[✔] CONFIRMED: Hidden Instagram account identified',
      '[FOUND] Username: @zaddyyzaynlubs',
      '[URL] https://www.instagram.com/zaddyyzaynlubs',
      '[PROFILE] Account successfully located and analyzed',
      '[STATUS] Profile accessibility: PUBLIC',
      '[ACTIVITY] Account shows recent activity patterns',
      '[METADATA] Digital fingerprint extracted successfully',
      '[VERIFICATION] Cross-platform identity confirmed',
      '[BEHAVIOR] Social media presence established',
      '',
      '[RECOMMENDATION] Monitor discreetly - DO NOT ENGAGE',
      '[SECURITY] Evidence archived in encrypted vault',
      '[LOG] Investigation completed successfully',
      '[STATUS] Returning to surveillance mode...',
    ];

    steps.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);

        // Different sound effects for different types of messages
        let soundType = 'normal';
        if (line.includes('CRITICAL') || line.includes('ALERT') || line.includes('BREACH')) {
          soundType = 'critical';
        } else if (line.includes('FOUND') || line.includes('CONFIRMED') || line.includes('✔')) {
          soundType = 'success';
        } else if (line.includes('WARNING') || line.includes('DECRYPT') || line.includes('MATCH')) {
          soundType = 'warning';
        }

        playBeep(soundType);

        // Add dramatic glitch effects during critical moments
        if (line.includes('CRITICAL ALERT') || line.includes('CONFIRMED') || line.includes('BREACH')) {
          setGlitchIntensity(6);
          setTimeout(() => setGlitchIntensity(1), 800);
        } else if (line.includes('FOUND') || line.includes('DECRYPT') || line.includes('HIGH CONFIDENCE')) {
          setGlitchIntensity(4);
          setTimeout(() => setGlitchIntensity(1), 400);
        } else if (line.includes('MATCH') || line.includes('WARNING')) {
          setGlitchIntensity(2);
          setTimeout(() => setGlitchIntensity(0), 200);
        }

        if (index === steps.length - 1) {
          setProcessing(false);
          setGlitchIntensity(0);
        }
      }, index * 900); // Faster pace for more dramatic effect
    });
  };

  return (
    <div
      style={{
        backgroundColor: '#000',
        color: '#00ff88',
        fontFamily: '"Fira Code", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", monospace',
        minHeight: '100vh',
        padding: '20px',
        overflow: 'hidden',
        position: 'relative',
        animation: glitchIntensity > 0 ? `glitchAnim ${0.1 + glitchIntensity * 0.05}s infinite step-start` : 'none',
      }}
    >
      <style>{`
        @keyframes glitchAnim {
          0% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(0);
          }
          10% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          20% {
            text-shadow: 2px -2px #ff0000, -2px 2px #00ffff;
            transform: translate(2px, -1px);
          }
          30% {
            text-shadow: -2px 2px #ff0000, 2px -2px #00ffff;
            transform: translate(-1px, 2px);
          }
          40% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(1px, -2px);
          }
          50% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          60% {
            text-shadow: 2px -2px #ff0000, -2px 2px #00ffff;
            transform: translate(2px, -1px);
          }
          70% {
            text-shadow: -2px 2px #ff0000, 2px -2px #00ffff;
            transform: translate(-1px, 2px);
          }
          80% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(1px, -1px);
          }
          90% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          100% {
            text-shadow: none;
            transform: translate(0);
          }
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        .terminal-cursor {
          animation: blink 1s infinite;
        }

        .critical-line {
          color: #ff4444;
          font-weight: bold;
        }

        .success-line {
          color: #44ff44;
          font-weight: bold;
        }

        .warning-line {
          color: #ffaa00;
          font-weight: bold;
        }
      `}</style>

      <div style={{
        borderBottom: '1px solid #00ff88',
        paddingBottom: '10px',
        marginBottom: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h2 style={{ margin: 0 }}>⚠️ MetaTrace Terminal - vGhostMode.91</h2>
        <div style={{ fontSize: '12px', opacity: 0.7 }}>
          {processing ? '🔍 SCANNING...' : '⚡ READY'}
        </div>
      </div>

      <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.4' }}>
        {logs.map((line, index) => {
          let className = '';
          if (line.includes('[FOUND]') || line.includes('HIGH CONFIDENCE') || line.includes('✔')) {
            className = 'success-line';
          } else if (line.includes('[ALERT]') || line.includes('[DECRYPT]') || line.includes('SUSPICIOUS')) {
            className = 'critical-line';
          } else if (line.includes('[PROMPT]') || line.includes('[ANALYZE]')) {
            className = 'warning-line';
          }

          return (
            <div key={index} className={className} style={{ marginBottom: '2px' }}>
              {line}
            </div>
          );
        })}

        {processing && (
          <div style={{ marginTop: '10px', opacity: 0.8 }}>
            <span className="terminal-cursor">█</span> Processing...
          </div>
        )}

        {!processing && (
          <div style={{ display: 'flex', alignItems: 'center', marginTop: '15px' }}>
            <span style={{ color: '#ffaa00' }}>{'> '}</span>
            <input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleInput}
              style={{
                backgroundColor: 'transparent',
                color: '#00ff88',
                border: 'none',
                outline: 'none',
                fontFamily: '"Fira Code", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", monospace',
                fontSize: '16px',
                flex: 1,
                caretColor: '#00ff88',
              }}
              placeholder={confirmed ? "Session terminated..." : ""}
              disabled={confirmed}
              autoFocus
            />
            <span className="terminal-cursor" style={{ marginLeft: '5px' }}>█</span>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
