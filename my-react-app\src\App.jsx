import React, { useState, useEffect, useRef } from 'react';

function App() {
  const [logs, setLogs] = useState([]);
  const [input, setInput] = useState('');
  const [processing, setProcessing] = useState(false);
  const [confirmed, setConfirmed] = useState(false);
  const [glitchIntensity, setGlitchIntensity] = useState(0);
  const audioContextRef = useRef(null);

  useEffect(() => {
    // Initialize audio context for beep sounds
    try {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported');
    }
    glitchIntro();
  }, []);

  const glitchIntro = () => {
    const intro = [
      '[BOOT] Initializing Surveillance Framework v3.7.2...',
      '[KERNEL] Loading neural network modules... OK',
      '[MEMORY] Allocating 2048MB for deep scan operations... OK',
      '[NETWORK] Establishing encrypted tunnels to data centers...',
      '[PROXY] Routing through 7 international proxy servers...',
      '[STEALTH] Activating ghost mode protocols...',
      '[DATABASE] Connecting to global social media archives...',
      '[AI] Loading behavioral pattern recognition engine...',
      '[CRYPTO] Decrypting protected metadata streams...',
      '[SYS] Social Graph API initialized successfully',
      '[LOG] Backdoor loaded. Injecting trace listeners...',
      '[MONITOR] Real-time activity surveillance: ACTIVE',
      '[SCANNER] Cross-platform profile correlation: READY',
      '[STATUS] Access granted to linked metadata repositories',
      '[ALERT] Anomalous digital footprint patterns detected',
      '[WARNING] Potential shadow account activity identified',
      '[SCAN] Cross-referencing 847,293 shadow profiles...',
      '[ANALYSIS] Behavioral fingerprint matching in progress...',
      '[INTEL] Suspicious Instagram activity patterns detected',
      '[TARGET] Potential hidden account identified in network',
      '[READY] All systems operational. Surveillance mode: ACTIVE',
      '',
      '[🔍 DETECTION ALERT 🔍]',
      '[PROMPT] Possible unknown Instagram account detected. Wanna investigate?',
    ];

    intro.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);
        playBeep();
        // Increase glitch intensity during critical moments
        if (line.includes('ALERT') || line.includes('WARNING') || line.includes('READY')) {
          setGlitchIntensity(Math.random() * 4 + 1);
          setTimeout(() => setGlitchIntensity(0), 300);
        }
      }, index * 800);
    });
  };

  const playBeep = (type = 'normal') => {
    if (!audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);

    // Different beep types for different events
    let frequency = 800;
    let duration = 0.1;
    let volume = 0.1;

    switch (type) {
      case 'critical':
        frequency = 1200;
        duration = 0.2;
        volume = 0.15;
        break;
      case 'success':
        frequency = 600;
        duration = 0.15;
        volume = 0.12;
        break;
      case 'warning':
        frequency = 1000;
        duration = 0.12;
        volume = 0.13;
        break;
      default:
        frequency = 800;
        duration = 0.08;
        volume = 0.08;
    }

    oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
    gainNode.gain.setValueAtTime(volume, audioContextRef.current.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContextRef.current.currentTime + duration);

    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + duration);
  };

  const handleInput = (e) => {
    if (e.key === 'Enter') {
      const command = input.trim().toLowerCase().replace(/"/g, '');
      setLogs((prev) => [...prev, `> ${command}`]);
      setInput('');
      playBeep();

      if (!confirmed && (command === '&find' || command === 'find' || command === '&scan')) {
        setConfirmed(true);
        simulateFinding();
      } else if (!confirmed) {
        setLogs((prev) => [...prev, '[ERROR] Invalid command. Access denied.']);
      } else {
        setLogs((prev) => [...prev, '[SYSTEM] Session terminated. Restart required.']);
      }
    }
  };

  const simulateFinding = () => {
    setProcessing(true);
    const steps = [
      '[INIT] Launching deep reconnaissance protocols v4.7.3 - Initializing advanced surveillance framework with quantum encryption bypass capabilities...',
      '[KERNEL] Loading neural network surveillance modules - AI pattern recognition engine v8.2.1 with behavioral analysis algorithms activated...',
      '[MEMORY] Allocating 8192MB for deep pattern analysis - Reserved memory blocks: 0x7F8B4C000000-0x7F8B4C800000 for real-time data processing...',
      '[STEALTH] Activating invisible scanning mode - GHOST PROTOCOL enabled with advanced cloaking algorithms to avoid detection by security systems...',
      '[PROXY] Routing through 12 international proxy servers - US(Seattle)->DE(Berlin)->JP(Tokyo)->BR(São Paulo)->CA(Toronto)->RU(Moscow)->SG(Singapore)...',
      '[VPN] Establishing encrypted tunnels: US->DE->JP->BR->CA with AES-256 encryption and perfect forward secrecy protocols for maximum anonymity...',
      '[NETWORK] Penetrating Instagram API firewalls - Bypassing rate limiting, CAPTCHA systems, and advanced bot detection mechanisms...',
      '[BYPASS] Circumventing Meta security protection layers - Exploiting OAuth vulnerabilities and session token manipulation techniques...',
      '[EXPLOIT] Using zero-day vulnerability CVE-2024-GHOST - Remote code execution exploit targeting Instagram\'s image processing pipeline...',
      '[ACCESS] Gained root access to restricted databases - Compromised Instagram user repository with 2.8 billion user records and metadata...',
      '[DATABASE] Connected to Instagram user repository - Accessing primary database cluster with 847TB of user data, posts, stories, and private messages...',
      '[CRAWLER] Initializing distributed web crawling bots - Deploying 2,847 autonomous crawlers across 47 data centers worldwide for comprehensive data harvesting...',
      '[SCAN] Scanning 47,293,847 active user profiles - Processing user data at 15.7GB/sec with parallel processing across 128 CPU cores and GPU acceleration...',
      '[FILTER] Applying advanced behavioral pattern algorithms - Neural network analysis of posting habits, interaction patterns, and linguistic fingerprints...',
      '[AI] Machine learning model analyzing digital footprints - Deep learning algorithms processing biometric data, typing patterns, and social behavior analysis...',
      '[TRACE] Following encrypted digital breadcrumbs - Decrypting metadata trails, session tokens, and hidden communication channels across platforms...',
      '[DECRYPT] Breaking AES-256 encryption barriers - Quantum computing algorithms cracking encryption keys in real-time with 99.7% success rate...',
      '[HASH] Cracking SHA-512 password hashes - Rainbow table attacks and dictionary brute force methods revealing plaintext credentials...',
      '[METADATA] Extracting hidden EXIF data from 2,847 images - GPS coordinates, device information, camera settings, and timestamp analysis...',
      '[GEOLOC] Triangulating GPS coordinates from post metadata - Cross-referencing location data with cellular tower logs and WiFi access points...',
      '[TIMELINE] Analyzing posting frequency patterns across 6 months...',
      '[CONTACTS] Cross-referencing 1,247 mutual connections...',
      '[DEVICE] Fingerprinting unique device signatures...',
      '[BROWSER] Analyzing user-agent strings and screen resolutions...',
      '[IP] Tracing network routing patterns through ISP logs...',
      '[BEHAVIOR] Matching linguistic patterns using NLP algorithms...',
      '[SENTIMENT] Analyzing emotional patterns in post captions...',
      '[HASHTAG] Cross-referencing hashtag usage patterns...',
      '[STORY] Analyzing Instagram story viewing patterns...',
      '[LIKES] Mapping like/comment interaction networks...',
      '[TIMING] Correlating online activity with timezone data...',
      '[FACIAL] Running facial recognition on profile pictures...',
      '[VOICE] Analyzing voice patterns from story audio...',
      '[LOCATION] Cross-referencing check-in locations...',
      '[FRIENDS] Mapping social network connection graphs...',
      '[CORRELATION] Found 97.3% behavioral similarity match...',
      '[PATTERN] Identical posting schedule detected - CONFIDENCE: 99.1%',
      '[MATCH] Hidden Instagram account detected in network!',
      '[BREACH] Accessing private message history archives...',
      '[EXTRACT] Downloading complete profile metadata...',
      '[ANALYZE] Processing 15,847 unique data points...',
      '[ALGORITHM] Running advanced pattern recognition...',
      '[BIOMETRIC] Analyzing typing patterns and speed...',
      '[SOCIAL] Mapping complete social interaction graph...',
      '[TEMPORAL] Analyzing activity patterns across time zones...',
      '[LINGUISTIC] Comparing writing style fingerprints...',
      '[EMOTIONAL] Matching emotional expression patterns...',
      '[VISUAL] Analyzing photo composition and filter preferences...',
      '[AUDIO] Processing voice pattern recognition from stories...',
      '[NETWORK] Tracing connection paths through mutual friends...',
      '[LOCATION] GPS coordinates match 96.7% of the time',
      '[CONTACTS] 47 mutual friends identified in network',
      '[PHOTOS] Facial recognition: 98.9% match confidence',
      '[STYLE] Writing pattern analysis: IDENTICAL MATCH',
      '[TIMING] Active hours correlation: PERFECT SYNCHRONIZATION',
      '[DEVICE] Same device fingerprint detected across platforms',
      '[BROWSER] Identical browser configuration signatures',
      '[SECURITY] Same password patterns detected (encrypted)',
      '[EMAIL] Linked email addresses share common patterns',
      '[PHONE] Mobile device IMEI correlation: 94.2% match',
      '',
      '[⚠️ CRITICAL ALERT ⚠️] HIGH CONFIDENCE DETECTION',
      '[✔] CONFIRMED: Hidden Instagram account identified',
      '[FOUND] Username: @zaddyyzaynlubs',
      '[URL] https://www.instagram.com/zaddyyzaynlubs',
      '[PROFILE] Account successfully located and analyzed',
      '[STATUS] Profile accessibility: PUBLIC',
      '[ACTIVITY] Account shows recent activity patterns',
      '[METADATA] Digital fingerprint extracted successfully',
      '[VERIFICATION] Cross-platform identity confirmed',
      '[BEHAVIOR] Social media presence established',
      '[CONFIDENCE] Match probability: 98.9%',
      '[RISK] Detection risk: MINIMAL',
      '[STEALTH] Investigation conducted undetected',
      '',
      '[RECOMMENDATION] Monitor discreetly - DO NOT ENGAGE',
      '[SECURITY] Evidence archived in encrypted vault',
      '[BACKUP] Data replicated to secure offshore servers',
      '[CLEANUP] Removing traces of surveillance activity...',
      '[LOGS] Clearing access logs from target systems...',
      '[PROXY] Disconnecting from proxy network...',
      '[STEALTH] Returning to ghost mode...',
      '[LOG] Investigation completed successfully',
      '[STATUS] Returning to surveillance mode...',
    ];

    steps.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);

        // Different sound effects for different types of messages
        let soundType = 'normal';
        if (line.includes('CRITICAL') || line.includes('ALERT') || line.includes('BREACH')) {
          soundType = 'critical';
        } else if (line.includes('FOUND') || line.includes('CONFIRMED') || line.includes('✔')) {
          soundType = 'success';
        } else if (line.includes('WARNING') || line.includes('DECRYPT') || line.includes('MATCH')) {
          soundType = 'warning';
        }

        playBeep(soundType);

        // Add dramatic glitch effects during critical moments
        if (line.includes('CRITICAL ALERT') || line.includes('CONFIRMED') || line.includes('BREACH')) {
          setGlitchIntensity(6);
          setTimeout(() => setGlitchIntensity(1), 800);
        } else if (line.includes('FOUND') || line.includes('DECRYPT') || line.includes('HIGH CONFIDENCE')) {
          setGlitchIntensity(4);
          setTimeout(() => setGlitchIntensity(1), 400);
        } else if (line.includes('MATCH') || line.includes('WARNING')) {
          setGlitchIntensity(2);
          setTimeout(() => setGlitchIntensity(0), 200);
        }

        if (index === steps.length - 1) {
          setProcessing(false);
          setGlitchIntensity(0);
        }
      }, index * 900); // Faster pace for more dramatic effect
    });
  };

  return (
    <div
      style={{
        backgroundColor: '#000',
        color: '#00ff88',
        fontFamily: '"Fira Code", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", monospace',
        fontSize: '11px',
        minHeight: '100vh',
        padding: '8px',
        margin: '0',
        overflow: 'hidden',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        boxSizing: 'border-box',
        animation: glitchIntensity > 0 ? `glitchAnim ${0.1 + glitchIntensity * 0.05}s infinite step-start` : 'none',
      }}
    >
      <style>{`
        @keyframes glitchAnim {
          0% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(0);
          }
          10% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          20% {
            text-shadow: 2px -2px #ff0000, -2px 2px #00ffff;
            transform: translate(2px, -1px);
          }
          30% {
            text-shadow: -2px 2px #ff0000, 2px -2px #00ffff;
            transform: translate(-1px, 2px);
          }
          40% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(1px, -2px);
          }
          50% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          60% {
            text-shadow: 2px -2px #ff0000, -2px 2px #00ffff;
            transform: translate(2px, -1px);
          }
          70% {
            text-shadow: -2px 2px #ff0000, 2px -2px #00ffff;
            transform: translate(-1px, 2px);
          }
          80% {
            text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
            transform: translate(1px, -1px);
          }
          90% {
            text-shadow: -2px -2px #ff0000, 2px 2px #00ffff;
            transform: translate(-2px, 1px);
          }
          100% {
            text-shadow: none;
            transform: translate(0);
          }
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        .terminal-cursor {
          animation: blink 1s infinite;
        }

        .critical-line {
          color: #ff4444;
          font-weight: bold;
        }

        .success-line {
          color: #44ff44;
          font-weight: bold;
        }

        .warning-line {
          color: #ffaa00;
          font-weight: bold;
        }
      `}</style>

      <div style={{
        borderBottom: '1px solid #00ff88',
        paddingBottom: '5px',
        marginBottom: '8px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%'
      }}>
        <h2 style={{ margin: 0, fontSize: '14px' }}>⚠️ MetaTrace Terminal - vGhostMode.91</h2>
        <div style={{ fontSize: '10px', opacity: 0.7 }}>
          {processing ? '🔍 SCANNING...' : '⚡ READY'}
        </div>
      </div>

      <div style={{
        whiteSpace: 'pre-wrap',
        lineHeight: '1.1',
        width: '100%',
        maxWidth: '100vw',
        wordWrap: 'break-word',
        overflowWrap: 'break-word'
      }}>
        {logs.map((line, index) => {
          let className = '';
          if (line.includes('[FOUND]') || line.includes('HIGH CONFIDENCE') || line.includes('✔')) {
            className = 'success-line';
          } else if (line.includes('[ALERT]') || line.includes('[DECRYPT]') || line.includes('SUSPICIOUS')) {
            className = 'critical-line';
          } else if (line.includes('[PROMPT]') || line.includes('[ANALYZE]')) {
            className = 'warning-line';
          }

          return (
            <div key={index} className={className} style={{
              marginBottom: '2px',
              width: '100%',
              maxWidth: '100vw',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}>
              {line}
            </div>
          );
        })}

        {processing && (
          <div style={{ marginTop: '5px', opacity: 0.8 }}>
            <span className="terminal-cursor">█</span> Processing...
          </div>
        )}

        {!processing && (
          <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
            <span style={{ color: '#ffaa00' }}>{'> '}</span>
            <input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleInput}
              style={{
                backgroundColor: 'transparent',
                color: '#00ff88',
                border: 'none',
                outline: 'none',
                fontFamily: '"Fira Code", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", monospace',
                fontSize: '11px',
                flex: 1,
                caretColor: '#00ff88',
              }}
              placeholder={confirmed ? "Session terminated..." : ""}
              disabled={confirmed}
              autoFocus
            />
            <span className="terminal-cursor" style={{ marginLeft: '5px' }}>█</span>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
