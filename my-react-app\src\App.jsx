import React, { useState, useEffect } from 'react';

function App() {
  const [logs, setLogs] = useState([]);
  const [input, setInput] = useState('');
  const [processing, setProcessing] = useState(false);
  const [confirmed, setConfirmed] = useState(false);

  useEffect(() => {
    glitchIntro();
  }, []);

  const glitchIntro = () => {
    const intro = [
      '[BOOT] Initializing Surveillance Framework...',
      '[SYS] Connecting to Social Graph...',
      '[LOG] Backdoor loaded. Injecting trace listeners...',
      '[STATUS] Access granted to linked metadata...',
      '[PROMPT] Possible unknown social activity detected. Wanna investigate?',
    ];

    intro.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);
        playSound();
      }, index * 1000);
    });
  };

  const playSound = () => {
    const audio = new Audio("https://www.soundjay.com/button/beep-07.wav");
    audio.play();
  };

  const handleInput = (e) => {
    if (e.key === 'Enter') {
      const command = input.trim().toLowerCase().replace(/"/g, '');
      setLogs((prev) => [...prev, `> ${command}`]);
      setInput('');

      if (!confirmed && (command === 'yes' || command === 'y')) {
        setConfirmed(true);
        simulateFinding();
      } else {
        setLogs((prev) => [...prev, '[INFO] Unknown command. Type "yes" to continue.']);
      }
    }
  };

  const simulateFinding = () => {
    setProcessing(true);
    const steps = [
      '[TASK] Searching shadow profiles...',
      '[TRACE] Correlating contact sync data...',
      '[MATCH] Unregistered link found in shared messages...',
      '[INFO] Extracting hidden metadata...',
      '[ANALYZE] Account age: New | Activity: Low | Visibility: Hidden',
      '[✔] Probable alt-account detected',
      '[FOUND] Username: @shyy_girl_meta_23',
      '[LOG] Suspicious activity tagged. Recommendation: Observe discreetly.',
    ];

    steps.forEach((line, index) => {
      setTimeout(() => {
        setLogs((prev) => [...prev, line]);
        playSound();
        if (index === steps.length - 1) setProcessing(false);
      }, index * 1200);
    });
  };

  return (
    <div
      style={{
        backgroundColor: '#000',
        color: '#00ff88',
        fontFamily: 'monospace',
        minHeight: '100vh',
        padding: '20px',
        animation: 'glitchAnim 1s infinite step-start',
      }}
    >
      <style>{`
        @keyframes glitchAnim {
          0% { text-shadow: 2px 2px red; }
          20% { text-shadow: -2px -2px blue; }
          40% { text-shadow: 2px -2px green; }
          60% { text-shadow: -2px 2px yellow; }
          80% { text-shadow: 2px 2px purple; }
          100% { text-shadow: none; }
        }
      `}</style>

      <h2>⚠️ MetaTrace Terminal - vGhostMode.91</h2>
      <div style={{ whiteSpace: 'pre-wrap', marginTop: '20px' }}>
        {logs.map((line, index) => (
          <div key={index}>{line}</div>
        ))}
        {!processing && (
          <div style={{ display: 'flex', alignItems: 'center', marginTop: '10px' }}>
            <span>{'> '}</span>
            <input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleInput}
              style={{
                backgroundColor: 'transparent',
                color: '#00ff88',
                border: 'none',
                outline: 'none',
                fontFamily: 'monospace',
                fontSize: '16px',
                flex: 1,
              }}
              placeholder="Type yes to investigate..."
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
