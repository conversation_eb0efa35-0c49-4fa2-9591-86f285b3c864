{"version": 3, "file": "use-sound.cjs.development.js", "sources": ["../src/use-on-mount.ts", "../src/index.ts"], "sourcesContent": ["import * as React from 'react';\n\nexport default function useOnMount(callback: React.EffectCallback) {\n  React.useEffect(callback, []);\n}\n", "import React from 'react';\n\nimport useOnMount from './use-on-mount';\n\nimport { HookOptions, PlayOptions, PlayFunction, ReturnedValue } from './types';\n\nexport default function useSound<T = any>(\n  src: string | string[],\n  {\n    id,\n    volume = 1,\n    playbackRate = 1,\n    soundEnabled = true,\n    interrupt = false,\n    onload,\n    ...delegated\n  }: HookOptions<T> = {} as HookOptions\n) {\n  const HowlConstructor = React.useRef<HowlStatic | null>(null);\n  const isMounted = React.useRef(false);\n\n  const [duration, setDuration] = React.useState<number | null>(null);\n\n  const [sound, setSound] = React.useState<Howl | null>(null);\n\n  const handleLoad = function() {\n    if (typeof onload === 'function') {\n      // @ts-ignore\n      onload.call(this);\n    }\n\n    if (isMounted.current) {\n      // @ts-ignore\n      setDuration(this.duration() * 1000);\n    }\n\n    // @ts-ignore\n    setSound(this);\n  };\n\n  // We want to lazy-load How<PERSON>, since sounds can't play on load anyway.\n  useOnMount(() => {\n    import('howler').then(mod => {\n      if (!isMounted.current) {\n        // Depending on the module system used, `mod` might hold\n        // the export directly, or it might be under `default`.\n        HowlConstructor.current = mod.Howl ?? mod.default.Howl;\n\n        isMounted.current = true;\n\n        new HowlConstructor.current({\n          src: Array.isArray(src) ? src : [src],\n          volume,\n          rate: playbackRate,\n          onload: handleLoad,\n          ...delegated,\n        });\n      }\n    });\n\n    return () => {\n      isMounted.current = false;\n    };\n  });\n\n  // When the `src` changes, we have to do a whole thing where we recreate\n  // the Howl instance. This is because Howler doesn't expose a way to\n  // tweak the sound\n  React.useEffect(() => {\n    if (HowlConstructor.current && sound) {\n      setSound(\n        new HowlConstructor.current({\n          src: Array.isArray(src) ? src : [src],\n          volume,\n          onload: handleLoad,\n          ...delegated,\n        })\n      );\n    }\n    // The linter wants to run this effect whenever ANYTHING changes,\n    // but very specifically I only want to recreate the Howl instance\n    // when the `src` changes. Other changes should have no effect.\n    // Passing array to the useEffect dependencies list will result in\n    // ifinite loop so we need to stringify it, for more details check\n    // https://github.com/facebook/react/issues/14476#issuecomment-471199055\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [JSON.stringify(src)]);\n\n  // Whenever volume/playbackRate are changed, change those properties\n  // on the sound instance.\n  React.useEffect(() => {\n    if (sound) {\n      sound.volume(volume);\n\n      // HACK: When a sprite is defined, `sound.rate()` throws an error, because Howler tries to reset the \"_default\" sprite, which doesn't exist. This is likely a bug within Howler, but I don’t have the bandwidth to investigate, so instead, we’re ignoring playbackRate changes when a sprite is defined.\n      if (!delegated.sprite) {\n        sound.rate(playbackRate);\n      }\n    }\n  }, [sound, volume, playbackRate]);\n\n  const play: PlayFunction = React.useCallback(\n    (options?: PlayOptions) => {\n      if (typeof options === 'undefined') {\n        options = {};\n      }\n\n      if (!sound || (!soundEnabled && !options.forceSoundEnabled)) {\n        return;\n      }\n\n      if (interrupt) {\n        sound.stop();\n      }\n\n      if (options.playbackRate) {\n        sound.rate(options.playbackRate);\n      }\n\n      sound.play(options.id);\n    },\n    [sound, soundEnabled, interrupt]\n  );\n\n  const stop = React.useCallback(\n    id => {\n      if (!sound) {\n        return;\n      }\n      sound.stop(id);\n    },\n    [sound]\n  );\n\n  const pause = React.useCallback(\n    id => {\n      if (!sound) {\n        return;\n      }\n      sound.pause(id);\n    },\n    [sound]\n  );\n\n  const returnedValue: ReturnedValue = [\n    play,\n    {\n      sound,\n      stop,\n      pause,\n      duration,\n    },\n  ];\n\n  return returnedValue;\n}\n\nexport { useSound };\n"], "names": ["useOnMount", "callback", "React", "useSound", "src", "_temp", "id", "_ref$volume", "_ref", "volume", "_ref$playbackRate", "playbackRate", "_ref$soundEnabled", "soundEnabled", "_ref$interrupt", "interrupt", "onload", "delegated", "_objectWithoutPropertiesLoose", "_excluded", "HowlConstructor", "useRef", "isMounted", "_React$useState", "useState", "duration", "setDuration", "_React$useState2", "sound", "setSound", "handleLoad", "call", "current", "then", "mod", "_mod$Howl", "Howl", "_extends", "Array", "isArray", "rate", "useEffect", "JSON", "stringify", "sprite", "play", "useCallback", "options", "forceSoundEnabled", "stop", "pause", "returnedValue"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAEwBA,UAAUA,CAACC,QAA8B;EAC/DC,eAAe,CAACD,QAAQ,EAAE,EAAE,CAAC;AAC/B;;;ACJA,SAMwBE,QAAQA,CAC9BC,GAAsB,EAAAC,KAAA;gCASF,EAAiB,GAAAA,KAAA;IAPnCC,AAAEC,WAAA,GAAAC,IAAA,CACFC,MAAM;IAANA,MAAM,GAAAF,WAAA,cAAG,CAAC,GAAAA,WAAA;IAAAG,iBAAA,GAAAF,IAAA,CACVG,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,CAAC,GAAAA,iBAAA;IAAAE,iBAAA,GAAAJ,IAAA,CAChBK,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;IAAAE,cAAA,GAAAN,IAAA,CACnBO,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IACjBE,MAAM,GAAAR,IAAA,CAANQ,MAAM;IACHC,SAAS,GAAAC,6BAAA,CAAAV,IAAA,EAAAW,SAAA;EAGd,IAAMC,eAAe,GAAGlB,cAAK,CAACmB,MAAM,CAAoB,IAAI,CAAC;EAC7D,IAAMC,SAAS,GAAGpB,cAAK,CAACmB,MAAM,CAAC,KAAK,CAAC;EAErC,IAAAE,eAAA,GAAgCrB,cAAK,CAACsB,QAAQ,CAAgB,IAAI,CAAC;IAA5DC,QAAQ,GAAAF,eAAA;IAAEG,WAAW,GAAAH,eAAA;EAE5B,IAAAI,gBAAA,GAA0BzB,cAAK,CAACsB,QAAQ,CAAc,IAAI,CAAC;IAApDI,KAAK,GAAAD,gBAAA;IAAEE,QAAQ,GAAAF,gBAAA;EAEtB,IAAMG,UAAU,GAAG,SAAbA,UAAUA;IACd,IAAI,OAAOd,MAAM,KAAK,UAAU,EAAE;;MAEhCA,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC;;IAGnB,IAAIT,SAAS,CAACU,OAAO,EAAE;;MAErBN,WAAW,CAAC,IAAI,CAACD,QAAQ,EAAE,GAAG,IAAI,CAAC;;;IAIrCI,QAAQ,CAAC,IAAI,CAAC;GACf;;EAGD7B,UAAU,CAAC;IACT,mEAAO,QAAQ,OAAC,CAACiC,IAAI,CAAC,UAAAC,GAAG;MACvB,IAAI,CAACZ,SAAS,CAACU,OAAO,EAAE;QAAA,IAAAG,SAAA;;;QAGtBf,eAAe,CAACY,OAAO,IAAAG,SAAA,GAAGD,GAAG,CAACE,IAAI,cAAAD,SAAA,cAAAA,SAAA,GAAID,GAAG,WAAQ,CAACE,IAAI;QAEtDd,SAAS,CAACU,OAAO,GAAG,IAAI;QAExB,IAAIZ,eAAe,CAACY,OAAO,CAAAK,QAAA;UACzBjC,GAAG,EAAEkC,KAAK,CAACC,OAAO,CAACnC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;UACrCK,MAAM,EAANA,MAAM;UACN+B,IAAI,EAAE7B,YAAY;UAClBK,MAAM,EAAEc;WACLb,SAAS,CACb,CAAC;;KAEL,CAAC;IAEF,OAAO;MACLK,SAAS,CAACU,OAAO,GAAG,KAAK;KAC1B;GACF,CAAC;;;;EAKF9B,cAAK,CAACuC,SAAS,CAAC;IACd,IAAIrB,eAAe,CAACY,OAAO,IAAIJ,KAAK,EAAE;MACpCC,QAAQ,CACN,IAAIT,eAAe,CAACY,OAAO,CAAAK,QAAA;QACzBjC,GAAG,EAAEkC,KAAK,CAACC,OAAO,CAACnC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;QACrCK,MAAM,EAANA,MAAM;QACNO,MAAM,EAAEc;SACLb,SAAS,CACb,CAAC,CACH;;;;;;;;;GASJ,EAAE,CAACyB,IAAI,CAACC,SAAS,CAACvC,GAAG,CAAC,CAAC,CAAC;;;EAIzBF,cAAK,CAACuC,SAAS,CAAC;IACd,IAAIb,KAAK,EAAE;MACTA,KAAK,CAACnB,MAAM,CAACA,MAAM,CAAC;;MAGpB,IAAI,CAACQ,SAAS,CAAC2B,MAAM,EAAE;QACrBhB,KAAK,CAACY,IAAI,CAAC7B,YAAY,CAAC;;;GAG7B,EAAE,CAACiB,KAAK,EAAEnB,MAAM,EAAEE,YAAY,CAAC,CAAC;EAEjC,IAAMkC,IAAI,GAAiB3C,cAAK,CAAC4C,WAAW,CAC1C,UAACC,OAAqB;IACpB,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,GAAG,EAAE;;IAGd,IAAI,CAACnB,KAAK,IAAK,CAACf,YAAY,IAAI,CAACkC,OAAO,CAACC,iBAAkB,EAAE;MAC3D;;IAGF,IAAIjC,SAAS,EAAE;MACba,KAAK,CAACqB,IAAI,EAAE;;IAGd,IAAIF,OAAO,CAACpC,YAAY,EAAE;MACxBiB,KAAK,CAACY,IAAI,CAACO,OAAO,CAACpC,YAAY,CAAC;;IAGlCiB,KAAK,CAACiB,IAAI,CAACE,OAAO,CAACzC,EAAE,CAAC;GACvB,EACD,CAACsB,KAAK,EAAEf,YAAY,EAAEE,SAAS,CAAC,CACjC;EAED,IAAMkC,IAAI,GAAG/C,cAAK,CAAC4C,WAAW,CAC5B,UAAAxC,EAAE;IACA,IAAI,CAACsB,KAAK,EAAE;MACV;;IAEFA,KAAK,CAACqB,IAAI,CAAC3C,EAAE,CAAC;GACf,EACD,CAACsB,KAAK,CAAC,CACR;EAED,IAAMsB,KAAK,GAAGhD,cAAK,CAAC4C,WAAW,CAC7B,UAAAxC,EAAE;IACA,IAAI,CAACsB,KAAK,EAAE;MACV;;IAEFA,KAAK,CAACsB,KAAK,CAAC5C,EAAE,CAAC;GAChB,EACD,CAACsB,KAAK,CAAC,CACR;EAED,IAAMuB,aAAa,GAAkB,CACnCN,IAAI,EACJ;IACEjB,KAAK,EAALA,KAAK;IACLqB,IAAI,EAAJA,IAAI;IACJC,KAAK,EAALA,KAAK;IACLzB,QAAQ,EAARA;GACD,CACF;EAED,OAAO0B,aAAa;AACtB;;;;;"}